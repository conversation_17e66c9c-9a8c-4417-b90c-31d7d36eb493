import React, { useState } from "react";
import {
  Layout,
  Button,
  Badge,
  Progress,
  Input,
  Dropdown,
  Space,
  Divider,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  MoreOutlined,
  AppstoreOutlined,
  CrownOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation } from "react-router-dom";
import WorkspaceSelector from "./Workspace/WorkspaceSelector";
import PremiumFeatureModal from "./PremiumFeatureModal";

const { Sider } = Layout;

const DashboardSidebar = ({
  collapsed,
  selectedBoard,
  boards,
  workspaces,
  selectedWorkspace,
  onSelectWorkspace,
  onCreateWorkspace,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onManageMembers,
  leaveAssignedWorkspace,
  onSelectBoard,
  createBoard,
  onUpdateBoardName,
  onDeleteBoard,
  isMobile,
  visible,
  onClose,
  loading,
}) => {
  const [editingBoard, setEditingBoard] = useState(null);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Giới hạn board cho Free plan
  const FREE_BOARD_LIMIT = 5;

  const getBoardMenuItems = (boardId, boardName) => [
    {
      key: "delete",
      label: (
        <Popconfirm
          title="Xác nhận xóa Board"
          description={`Bạn có chắc chắn muốn xóa?`}
          onConfirm={() => onDeleteBoard(boardId)}
          okText="Xóa"
          cancelText="Hủy"
          okType="danger"
          placement="bottom"
        >
          <div className="dropdown-item delete-item">
            <DeleteOutlined />
            <span>Xóa</span>
          </div>
        </Popconfirm>
      ),
    },
  ];

  const handleUpdateBoardName = (boardId, newName) => {
    if (onUpdateBoardName) {
      onUpdateBoardName(boardId, newName);
    }
    setEditingBoard(null);
  };

  const handleSelectBoard = (boardId) => {
    onSelectBoard(boardId);
    // Navigate to dashboard when selecting board
    navigate("/app/dashboard");
    // Close mobile sidebar when selecting board
    if (isMobile && onClose) {
      onClose();
    }
  };

  const handleCreateBoard = () => {
    if (!selectedWorkspace) {
      return;
    }

    // Kiểm tra giới hạn board cho Free plan
    if (workspaceBoards.length >= FREE_BOARD_LIMIT) {
      setShowPremiumModal(true);
      return;
    }

    // Tạo board bình thường nếu chưa đạt giới hạn
    createBoard(selectedWorkspace);
  };

  // Boards are already filtered by workspace from the API
  const workspaceBoards = boards;

  // check if the user is the owner of the workspace
  const currentWorkspace = workspaces.find((ws) => ws.id === selectedWorkspace);
  const isUserWorkspaceOwner = currentWorkspace?.isOwned || false;

  const handleTaskAssignClick = () => {
    navigate("/app/task-assign");
    // Close mobile sidebar when selecting
    if (isMobile && onClose) {
      onClose();
    }
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className="dashboard-sidebar"
      width={280}
    >
      <div className="sidebar-content">
        {!collapsed && (
          <>
            {/* App Logo */}
            <div className="sidebar-logo">
              <div className="logo-container">
                <div className="logo-icon">
                  <AppstoreOutlined />
                </div>
                <span className="logo-text">TaskFlow</span>
              </div>
            </div>

            {/* Workspace Selector */}
            <div className="sidebar-section workspace-section">
              <WorkspaceSelector
                workspaces={workspaces}
                selectedWorkspace={selectedWorkspace}
                onSelectWorkspace={onSelectWorkspace}
                onCreateWorkspace={onCreateWorkspace}
                onUpdateWorkspace={onUpdateWorkspace}
                onDeleteWorkspace={onDeleteWorkspace}
                onManageMembers={onManageMembers}
                leaveAssignedWorkspace={leaveAssignedWorkspace}
                loading={loading}
              />
            </div>

            <Divider style={{ margin: "12px 0" }} />

            {/* Your tasks */}
            <div className="sidebar-section">
              <div className="section-header">
                <h3>Các task được giao</h3>
              </div>
              <div className="section-content">
                <div className="board-menu-item">
                  <div
                    className={`board-item-content ${
                      location.pathname === "/app/task-assign" ? "selected" : ""
                    }`}
                    onClick={handleTaskAssignClick}
                    style={{ cursor: "pointer" }}
                  >
                    <div className="list-info">
                      <div
                        className="list-color"
                        style={{
                          backgroundColor: "#1890ff",
                        }}
                      ></div>
                      <span className="list-name">Task được giao</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Divider style={{ margin: "12px 0" }} />

            {/* Boards Section */}
            <div className="sidebar-section">
              <div className="section-header">
                <h3>Các board của bạn</h3>
                {isUserWorkspaceOwner && (
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    size="small"
                    onClick={handleCreateBoard}
                    disabled={!selectedWorkspace}
                    title={
                      !selectedWorkspace
                        ? "Vui lòng chọn workspace trước"
                        : "Tạo board mới"
                    }
                  />
                )}
              </div>

              <div className="boards-container">
                {workspaceBoards.length === 0 && selectedWorkspace ? (
                  isUserWorkspaceOwner ? (
                    <div className="empty-boards">
                      <p>Chưa có board nào trong workspace</p>
                      <Button
                        type="dashed"
                        icon={<PlusOutlined />}
                        onClick={handleCreateBoard}
                        block
                      >
                        Tạo board đầu tiên
                      </Button>
                    </div>
                  ) : (
                    <div className="empty-boards">
                      <p>Bạn chưa được thêm vào board nào</p>
                    </div>
                  )
                ) : (
                  workspaceBoards.map((board) => (
                    <div key={board.id} className="board-menu-item">
                      <div
                        className={`board-item-content ${
                          selectedBoard === board.id &&
                          location.pathname === "/app/dashboard"
                            ? "selected"
                            : ""
                        }`}
                        onClick={() => handleSelectBoard(board.id)}
                      >
                        <div className="list-info">
                          <div
                            className="list-color"
                            style={{
                              backgroundColor: board.isGoogleSynced
                                ? "#4285f4"
                                : "#1890ff",
                            }}
                          ></div>
                          {editingBoard === board.id && isUserWorkspaceOwner ? (
                            <Input
                              defaultValue={board.name}
                              className="board-name-input"
                              size="small"
                              onClick={(e) => e.stopPropagation()}
                              onPressEnter={(e) =>
                                handleUpdateBoardName(board.id, e.target.value)
                              }
                              onBlur={(e) =>
                                handleUpdateBoardName(board.id, e.target.value)
                              }
                              autoFocus
                            />
                          ) : (
                            <span
                              className="list-name editable"
                              onDoubleClick={(e) => {
                                e.stopPropagation();
                                setEditingBoard(board.id);
                              }}
                            >
                              {board.name}
                            </span>
                          )}
                        </div>
                      </div>
                      {/* Hiển thị nút xóa nếu không phải board mặc định */}
                      {board.isDefault ? null : (
                        <Dropdown
                          menu={{
                            items: getBoardMenuItems(board.id, board.name),
                          }}
                          trigger={["click"]}
                          placement="bottomRight"
                        >
                          <Button
                            type="text"
                            icon={<MoreOutlined />}
                            size="small"
                            className="board-menu-btn"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Dropdown>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Premium Feature Modal */}
      <PremiumFeatureModal
        visible={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        featureType="MULTIPLE_BOARDS"
        onLearnMore={() => {
          console.log("Learn more about Premium");
          // Có thể redirect đến trang thông tin premium
        }}
        onTryPremium={() => {
          console.log("Try Premium for free");
          // Có thể redirect đến trang đăng ký premium
        }}
      />
    </Sider>
  );
};

export default DashboardSidebar;
