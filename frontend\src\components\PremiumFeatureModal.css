/* Premium Feature Modal Styles */
.premium-feature-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.premium-feature-modal .ant-modal-body {
  padding: 0;
}

.premium-modal-content {
  padding: 32px;
  text-align: center;
  background: #ffffff;
}

/* Header Styles */
.premium-modal-header {
  margin-bottom: 32px;
}

.premium-modal-title {
  color: #262626;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px !important;
  line-height: 1.3;
}

.premium-modal-description {
  color: #8c8c8c;
  font-size: 16px;
  line-height: 1.5;
  display: block;
}

/* Illustration Styles */
.premium-modal-illustration {
  margin: 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 280px;
  position: relative;
}

.boards-stack {
  position: relative;
  width: 320px;
  height: 240px;
  perspective: 1000px;
}

.board {
  position: absolute;
  width: 280px;
  height: 200px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  background: #ffffff;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.board-3 {
  background: #434343;
  transform: translateX(-20px) translateY(-20px) rotateY(-8deg) rotateX(2deg);
  z-index: 1;
}

.board-2 {
  background: #1890ff;
  transform: translateX(-10px) translateY(-10px) rotateY(-4deg) rotateX(1deg);
  z-index: 2;
}

.board-1 {
  background: #ffffff;
  transform: translateX(0px) translateY(0px) rotateY(0deg) rotateX(0deg);
  z-index: 3;
  position: relative;
}

.board-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.board-3 .board-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.board-2 .board-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.board-1 .board-header {
  border-bottom-color: #f0f0f0;
}

.board-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.board-1 .board-title {
  color: #262626;
}

.board-tabs {
  display: flex;
  gap: 4px;
}

.tab {
  width: 24px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.6);
}

.board-1 .tab {
  background: #d9d9d9;
}

.board-content {
  padding: 16px;
  display: flex;
  gap: 12px;
  height: calc(100% - 45px);
}

.task-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  height: 24px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.board-1 .task-item {
  background: #fafafa;
  border-color: #f0f0f0;
}

.board-background {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #ff7875 0%, #ffa940 50%, #52c41a 100%);
  opacity: 0.3;
  border-radius: 8px 0 12px 0;
}

/* Members Illustration Styles */
.members-illustration {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 320px;
  height: 240px;
}

.team-workspace {
  width: 280px;
  height: 200px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid #f0f0f0;
  padding: 20px;
  position: relative;
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.workspace-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.member-count {
  font-size: 12px;
  color: #8c8c8c;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #52c41a);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.member-avatar.member-1 {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}
.member-avatar.member-2 {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}
.member-avatar.member-3 {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}
.member-avatar.member-4 {
  background: linear-gradient(135deg, #f759ab, #ff85c0);
}
.member-avatar.member-5 {
  background: linear-gradient(135deg, #722ed1, #9254de);
}
.member-avatar.member-6 {
  background: linear-gradient(135deg, #fa541c, #ff7a45);
}

.member-avatar.member-plus {
  background: #f0f0f0;
  color: #8c8c8c;
  border: 2px dashed #d9d9d9;
}

.collaboration-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-icon {
  width: 12px;
  height: 12px;
  background: #52c41a;
  border-radius: 50%;
  position: relative;
}

.feature-icon::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 8px;
  font-weight: bold;
}

.feature-text {
  font-size: 12px;
  color: #595959;
}

/* Features Illustration Styles */
.features-illustration {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 320px;
  height: 240px;
}

.features-dashboard {
  width: 280px;
  height: 200px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid #f0f0f0;
  padding: 16px;
  position: relative;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.premium-badge {
  background: linear-gradient(135deg, #faad14, #ffc53d);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(250, 173, 20, 0.3);
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.feature-card {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  position: relative;
}

.feature-card.analytics {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border-color: #91d5ff;
}

.feature-card.automation {
  background: linear-gradient(135deg, #f6ffed, #f0fff0);
  border-color: #b7eb8f;
}

.feature-card.integrations {
  background: linear-gradient(135deg, #fff2e8, #fff7e6);
  border-color: #ffd591;
}

.card-icon {
  width: 16px;
  height: 16px;
  background: #1890ff;
  border-radius: 4px;
  margin-bottom: 8px;
}

.automation .card-icon {
  background: #52c41a;
}

.integrations .card-icon {
  background: #faad14;
}

.card-title {
  font-size: 11px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 3px;
  height: 20px;
}

.bar {
  width: 8px;
  background: #1890ff;
  border-radius: 2px 2px 0 0;
}

.bar-1 {
  height: 60%;
}
.bar-2 {
  height: 80%;
}
.bar-3 {
  height: 40%;
}
.bar-4 {
  height: 100%;
}

.automation-flow {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px;
}

.flow-step {
  width: 12px;
  height: 12px;
  background: #52c41a;
  border-radius: 50%;
}

.flow-arrow {
  width: 8px;
  height: 2px;
  background: #52c41a;
  position: relative;
}

.flow-arrow::after {
  content: "";
  position: absolute;
  right: -2px;
  top: -1px;
  width: 0;
  height: 0;
  border-left: 4px solid #52c41a;
  border-top: 2px solid transparent;
  border-bottom: 2px solid transparent;
}

.integration-logos {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  height: 20px;
}

.logo {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.logo-1 {
  background: #1890ff;
}
.logo-2 {
  background: #52c41a;
}
.logo-3 {
  background: #faad14;
}
.logo-4 {
  background: #f759ab;
}

/* Footer Styles */
.premium-modal-footer {
  margin-top: 40px;
}

.learn-more-btn {
  color: #1890ff !important;
  font-size: 14px;
  padding: 8px 16px;
  height: auto;
  border: none;
  background: transparent;
}

.learn-more-btn:hover {
  color: #40a9ff !important;
  background: rgba(24, 144, 255, 0.04);
}

.try-premium-btn {
  background: #1890ff;
  border-color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 32px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.try-premium-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* Responsive */
@media (max-width: 576px) {
  .premium-modal-content {
    padding: 24px 20px;
  }

  .premium-modal-title {
    font-size: 20px;
  }

  .premium-modal-description {
    font-size: 14px;
  }

  .boards-stack {
    width: 280px;
    height: 200px;
  }

  .board {
    width: 240px;
    height: 160px;
  }

  .premium-modal-footer .ant-space {
    flex-direction: column;
    width: 100%;
  }

  .try-premium-btn {
    width: 100%;
  }
}
