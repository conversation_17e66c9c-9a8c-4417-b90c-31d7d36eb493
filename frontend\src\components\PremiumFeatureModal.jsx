import React from "react";
import { Mo<PERSON>, <PERSON>po<PERSON>, Button, Space } from "antd";
import "./PremiumFeatureModal.css";

const { Title, Text } = Typography;

// Predefined feature configurations
const PREMIUM_FEATURES = {
  MULTIPLE_BOARDS: {
    title: "Multiple boards is a Premium Feature",
    description:
      "Create multiple boards for each of your projects to keep everything organized.",
    illustration: "boards",
  },
  TEAM_MEMBERS: {
    title: "Team collaboration is a Premium Feature",
    description:
      "Invite unlimited team members and collaborate on projects together with advanced permissions.",
    illustration: "members",
  },
  ADVANCED_FEATURES: {
    title: "Advanced features are Premium only",
    description:
      "Unlock powerful tools like analytics, automation, custom fields and advanced integrations.",
    illustration: "features",
  },
};

const PremiumFeatureModal = ({
  visible,
  onClose,
  featureType, // New prop for predefined features
  title, // Custom title (overrides featureType)
  description, // Custom description (overrides featureType)
  illustration = "boards", // boards, members, features
  onLearnMore,
  onTryPremium,
}) => {
  // Get configuration from predefined features or use custom props
  const config = featureType ? PREMIUM_FEATURES[featureType] : {};
  const finalTitle = title || config.title || "This is a Premium Feature";
  const finalDescription =
    description ||
    config.description ||
    "Upgrade to Premium to unlock this feature.";
  const finalIllustration = illustration || config.illustration || "boards";
  const handleLearnMore = () => {
    if (onLearnMore) {
      onLearnMore();
    } else {
      // Default action - có thể redirect đến trang premium info
      console.log("Learn more about Premium");
    }
  };

  const handleTryPremium = () => {
    if (onTryPremium) {
      onTryPremium();
    } else {
      // Default action - có thể redirect đến trang đăng ký premium
      console.log("Try Premium for free");
    }
  };

  // Render different illustrations based on type
  const renderIllustration = () => {
    switch (finalIllustration) {
      case "members":
        return renderMembersIllustration();
      case "features":
        return renderFeaturesIllustration();
      case "boards":
      default:
        return renderBoardsIllustration();
    }
  };

  const renderBoardsIllustration = () => (
    <div className="boards-stack">
      {/* Board 3 (back) */}
      <div className="board board-3">
        <div className="board-header">
          <div className="board-title">Secret Project</div>
          <div className="board-tabs">
            <div className="tab"></div>
            <div className="tab"></div>
            <div className="tab"></div>
          </div>
        </div>
      </div>

      {/* Board 2 (middle) */}
      <div className="board board-2">
        <div className="board-header">
          <div className="board-title">Personal Tasks</div>
          <div className="board-tabs">
            <div className="tab"></div>
            <div className="tab"></div>
            <div className="tab"></div>
          </div>
        </div>
        <div className="board-content">
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
        </div>
      </div>

      {/* Board 1 (front) */}
      <div className="board board-1">
        <div className="board-header">
          <div className="board-title">Work Project</div>
          <div className="board-tabs">
            <div className="tab"></div>
            <div className="tab"></div>
            <div className="tab"></div>
          </div>
        </div>
        <div className="board-content">
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
          <div className="task-column">
            <div className="task-item"></div>
            <div className="task-item"></div>
          </div>
        </div>
        {/* Background image overlay */}
        <div className="board-background"></div>
      </div>
    </div>
  );

  const renderMembersIllustration = () => (
    <div className="members-illustration">
      <div className="team-workspace">
        <div className="workspace-header">
          <div className="workspace-title">Team Workspace</div>
          <div className="member-count">12 members</div>
        </div>
        <div className="members-grid">
          <div className="member-avatar member-1"></div>
          <div className="member-avatar member-2"></div>
          <div className="member-avatar member-3"></div>
          <div className="member-avatar member-4"></div>
          <div className="member-avatar member-5"></div>
          <div className="member-avatar member-6"></div>
          <div className="member-avatar member-plus">+6</div>
        </div>
        <div className="collaboration-features">
          <div className="feature-item">
            <div className="feature-icon"></div>
            <div className="feature-text">Real-time collaboration</div>
          </div>
          <div className="feature-item">
            <div className="feature-icon"></div>
            <div className="feature-text">Advanced permissions</div>
          </div>
          <div className="feature-item">
            <div className="feature-icon"></div>
            <div className="feature-text">Team analytics</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFeaturesIllustration = () => (
    <div className="features-illustration">
      <div className="features-dashboard">
        <div className="dashboard-header">
          <div className="dashboard-title">Premium Dashboard</div>
          <div className="premium-badge">PRO</div>
        </div>
        <div className="features-grid">
          <div className="feature-card analytics">
            <div className="card-icon"></div>
            <div className="card-title">Analytics</div>
            <div className="chart-bars">
              <div className="bar bar-1"></div>
              <div className="bar bar-2"></div>
              <div className="bar bar-3"></div>
              <div className="bar bar-4"></div>
            </div>
          </div>
          <div className="feature-card automation">
            <div className="card-icon"></div>
            <div className="card-title">Automation</div>
            <div className="automation-flow">
              <div className="flow-step"></div>
              <div className="flow-arrow"></div>
              <div className="flow-step"></div>
              <div className="flow-arrow"></div>
              <div className="flow-step"></div>
            </div>
          </div>
          <div className="feature-card integrations">
            <div className="card-icon"></div>
            <div className="card-title">Integrations</div>
            <div className="integration-logos">
              <div className="logo logo-1"></div>
              <div className="logo logo-2"></div>
              <div className="logo logo-3"></div>
              <div className="logo logo-4"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      centered
      width={480}
      className="premium-feature-modal"
      closable={true}
    >
      <div className="premium-modal-content">
        {/* Header */}
        <div className="premium-modal-header">
          <Title level={3} className="premium-modal-title">
            {finalTitle}
          </Title>
          <Text className="premium-modal-description">{finalDescription}</Text>
        </div>

        {/* Illustration */}
        <div className="premium-modal-illustration">{renderIllustration()}</div>

        {/* Footer Actions */}
        <div className="premium-modal-footer">
          <Space
            size="middle"
            style={{ width: "100%", justifyContent: "center" }}
          >
            <Button
              type="text"
              onClick={handleLearnMore}
              className="learn-more-btn"
            >
              Learn more about Premium
            </Button>
            <Button
              type="primary"
              onClick={handleTryPremium}
              className="try-premium-btn"
              size="large"
            >
              Try Premium for free
            </Button>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default PremiumFeatureModal;
