import React, { useState } from "react";
import { Button, Space, Card, Typography } from "antd";
import { CrownOutlined, PlusOutlined } from "@ant-design/icons";
import PremiumFeatureModal from "./PremiumFeatureModal";

const { Title, Text } = Typography;

const PremiumFeatureDemo = () => {
  const [showMultipleBoardsModal, setShowMultipleBoardsModal] = useState(false);
  const [showCustomModal, setShowCustomModal] = useState(false);

  const handleLearnMore = () => {
    console.log("Redirecting to premium info page...");
    // <PERSON><PERSON> thể redirect đến trang thông tin premium
    // window.open('/premium-info', '_blank');
  };

  const handleTryPremium = () => {
    console.log("Redirecting to premium signup...");
    // C<PERSON> thể redirect đến trang đăng ký premium
    // window.open('/premium-signup', '_blank');
  };

  return (
    <div style={{ padding: "24px", maxWidth: "800px", margin: "0 auto" }}>
      <Card>
        <Title level={2}>
          <CrownOutlined style={{ color: "#faad14", marginRight: "8px" }} />
          Premium Feature Modal Demo
        </Title>
        
        <Text type="secondary" style={{ display: "block", marginBottom: "24px" }}>
          Đây là demo cho modal hiển thị tính năng Premium. Click vào các button bên dưới để xem modal.
        </Text>

        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          {/* Demo 1: Multiple Boards Feature */}
          <Card size="small" title="Multiple Boards Feature">
            <Text>
              Khi user cố gắng tạo board thứ 2 trở lên (tính năng premium)
            </Text>
            <br />
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setShowMultipleBoardsModal(true)}
              style={{ marginTop: "12px" }}
            >
              Tạo Board Mới (Premium)
            </Button>
          </Card>

          {/* Demo 2: Custom Premium Feature */}
          <Card size="small" title="Custom Premium Feature">
            <Text>
              Ví dụ với tính năng premium khác với title và description tùy chỉnh
            </Text>
            <br />
            <Button 
              type="primary" 
              icon={<CrownOutlined />}
              onClick={() => setShowCustomModal(true)}
              style={{ marginTop: "12px" }}
            >
              Advanced Analytics (Premium)
            </Button>
          </Card>

          {/* Usage Instructions */}
          <Card size="small" title="Cách sử dụng">
            <div style={{ fontSize: "14px", lineHeight: "1.6" }}>
              <Text strong>Props của PremiumFeatureModal:</Text>
              <ul style={{ marginTop: "8px", paddingLeft: "20px" }}>
                <li><code>visible</code>: Boolean - hiển thị/ẩn modal</li>
                <li><code>onClose</code>: Function - callback khi đóng modal</li>
                <li><code>title</code>: String - tiêu đề modal (optional)</li>
                <li><code>description</code>: String - mô tả tính năng (optional)</li>
                <li><code>onLearnMore</code>: Function - callback khi click "Learn more" (optional)</li>
                <li><code>onTryPremium</code>: Function - callback khi click "Try Premium" (optional)</li>
              </ul>
            </div>
          </Card>
        </Space>
      </Card>

      {/* Multiple Boards Modal */}
      <PremiumFeatureModal
        visible={showMultipleBoardsModal}
        onClose={() => setShowMultipleBoardsModal(false)}
        title="Multiple boards is a Premium Feature"
        description="Create multiple boards for each of your projects to keep everything organized."
        onLearnMore={handleLearnMore}
        onTryPremium={handleTryPremium}
      />

      {/* Custom Premium Feature Modal */}
      <PremiumFeatureModal
        visible={showCustomModal}
        onClose={() => setShowCustomModal(false)}
        title="Advanced Analytics is a Premium Feature"
        description="Get detailed insights and analytics for your projects with charts, reports, and team performance metrics."
        onLearnMore={handleLearnMore}
        onTryPremium={handleTryPremium}
      />
    </div>
  );
};

export default PremiumFeatureDemo;
