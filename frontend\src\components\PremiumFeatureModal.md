# PremiumFeatureModal Component

Modal component để hiển thị thông báo về tính năng Premium khi user cố gắng sử dụng tính năng bị giới hạn.

## Thiết kế

Modal được thiết kế theo style hiện đại với:
- Header có title và description
- Illustration hiển thị 3 boards xếp chồng với hiệu ứng 3D
- Footer có 2 buttons: "Learn more about Premium" và "Try Premium for free"
- Responsive design cho mobile

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `visible` | boolean | - | Hiển thị/ẩn modal |
| `onClose` | function | - | Callback khi đóng modal |
| `title` | string | "Multiple boards is a Premium Feature" | Tiêu đề modal |
| `description` | string | "Create multiple boards..." | <PERSON>ô tả tính năng |
| `onLearnMore` | function | - | Callback khi click "Learn more" |
| `onTryPremium` | function | - | Callback khi click "Try Premium" |

## <PERSON><PERSON><PERSON> sử dụng

### 1. Import component

```jsx
import PremiumFeatureModal from './components/PremiumFeatureModal';
```

### 2. Sử dụng cơ bản

```jsx
const [showModal, setShowModal] = useState(false);

return (
  <>
    <Button onClick={() => setShowModal(true)}>
      Tính năng Premium
    </Button>
    
    <PremiumFeatureModal
      visible={showModal}
      onClose={() => setShowModal(false)}
    />
  </>
);
```

### 3. Tùy chỉnh nội dung

```jsx
<PremiumFeatureModal
  visible={showModal}
  onClose={() => setShowModal(false)}
  title="Advanced Analytics is a Premium Feature"
  description="Get detailed insights and analytics for your projects."
  onLearnMore={() => window.open('/premium-info', '_blank')}
  onTryPremium={() => window.open('/premium-signup', '_blank')}
/>
```

### 4. Tích hợp với logic giới hạn

```jsx
const handleCreateWorkspace = () => {
  if (workspaces.length >= FREE_LIMIT) {
    setShowPremiumModal(true);
  } else {
    // Tạo workspace mới
    createWorkspace();
  }
};
```

## Ví dụ thực tế

### Giới hạn số lượng workspace

```jsx
const WorkspaceManager = () => {
  const [workspaces, setWorkspaces] = useState([]);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  
  const FREE_WORKSPACE_LIMIT = 1;
  
  const handleCreateWorkspace = () => {
    if (workspaces.length >= FREE_WORKSPACE_LIMIT) {
      setShowPremiumModal(true);
      return;
    }
    
    // Logic tạo workspace
    createNewWorkspace();
  };
  
  return (
    <>
      <Button onClick={handleCreateWorkspace}>
        Tạo Workspace Mới
      </Button>
      
      <PremiumFeatureModal
        visible={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        title="Multiple workspaces is a Premium Feature"
        description="Create unlimited workspaces for your projects."
      />
    </>
  );
};
```

### Giới hạn số lượng board

```jsx
const BoardManager = () => {
  const [boards, setBoards] = useState([]);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  
  const FREE_BOARD_LIMIT = 3;
  
  const handleCreateBoard = () => {
    if (boards.length >= FREE_BOARD_LIMIT) {
      setShowPremiumModal(true);
      return;
    }
    
    // Logic tạo board
    createNewBoard();
  };
  
  return (
    <>
      <Button onClick={handleCreateBoard}>
        Tạo Board Mới
      </Button>
      
      <PremiumFeatureModal
        visible={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        // Sử dụng default title và description
      />
    </>
  );
};
```

## Files

- `PremiumFeatureModal.jsx` - Component chính
- `PremiumFeatureModal.css` - Styles cho modal
- `PremiumFeatureDemo.jsx` - Demo component
- `WorkspaceWithPremiumLimit.jsx` - Ví dụ tích hợp thực tế

## Customization

Có thể tùy chỉnh styles bằng cách override CSS classes:

```css
.premium-feature-modal .premium-modal-title {
  color: your-color;
}

.premium-feature-modal .try-premium-btn {
  background: your-primary-color;
}
```
