import React, { useState } from "react";
import { Button, Card, Typography, Space, List, Tag } from "antd";
import { PlusOutlined, CrownOutlined } from "@ant-design/icons";
import PremiumFeatureModal from "./PremiumFeatureModal";

const { Title, Text } = Typography;

const WorkspaceWithPremiumLimit = () => {
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  
  // Mock data - giả sử user đã có 1 workspace (free plan chỉ cho phép 1)
  const [workspaces] = useState([
    { id: 1, name: "My First Workspace", isOwner: true }
  ]);
  
  const FREE_WORKSPACE_LIMIT = 1;
  const isAtLimit = workspaces.length >= FREE_WORKSPACE_LIMIT;

  const handleCreateWorkspace = () => {
    if (isAtLimit) {
      // Hiển thị modal premium thay vì tạo workspace
      setShowPremiumModal(true);
    } else {
      // Logic tạo workspace b<PERSON><PERSON> thường
      console.log("Creating new workspace...");
    }
  };

  const handleLearnMore = () => {
    console.log("Redirecting to premium features page...");
    // Có thể redirect đến trang thông tin chi tiết về premium
  };

  const handleTryPremium = () => {
    console.log("Redirecting to premium signup...");
    // Có thể redirect đến trang đăng ký premium
  };

  return (
    <div style={{ padding: "24px", maxWidth: "600px", margin: "0 auto" }}>
      <Card>
        <Title level={3}>Workspace Management</Title>
        
        <Text type="secondary" style={{ display: "block", marginBottom: "24px" }}>
          Quản lý các workspace của bạn. Free plan chỉ cho phép 1 workspace.
        </Text>

        {/* Current Workspaces */}
        <div style={{ marginBottom: "24px" }}>
          <Text strong style={{ display: "block", marginBottom: "12px" }}>
            Workspace hiện tại ({workspaces.length}/{FREE_WORKSPACE_LIMIT}):
          </Text>
          
          <List
            size="small"
            dataSource={workspaces}
            renderItem={(workspace) => (
              <List.Item>
                <Space>
                  <Text>{workspace.name}</Text>
                  {workspace.isOwner && <Tag color="blue">Owner</Tag>}
                </Space>
              </List.Item>
            )}
          />
        </div>

        {/* Create New Workspace Button */}
        <Space direction="vertical" style={{ width: "100%" }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateWorkspace}
            block
          >
            Tạo Workspace Mới
            {isAtLimit && <CrownOutlined style={{ marginLeft: "8px", color: "#faad14" }} />}
          </Button>
          
          {isAtLimit && (
            <Text type="secondary" style={{ fontSize: "12px", textAlign: "center", display: "block" }}>
              <CrownOutlined style={{ color: "#faad14", marginRight: "4px" }} />
              Bạn đã đạt giới hạn workspace cho Free plan
            </Text>
          )}
        </Space>

        {/* Usage Instructions */}
        <Card 
          size="small" 
          title="Cách hoạt động" 
          style={{ marginTop: "24px", background: "#fafafa" }}
        >
          <div style={{ fontSize: "14px", lineHeight: "1.6" }}>
            <Text>
              Khi user đã đạt giới hạn workspace (1 cho free plan), 
              việc click "Tạo Workspace Mới" sẽ hiển thị modal Premium 
              thay vì form tạo workspace.
            </Text>
            <br /><br />
            <Text strong>Logic kiểm tra:</Text>
            <ul style={{ marginTop: "8px", paddingLeft: "20px" }}>
              <li>Đếm số workspace hiện tại của user</li>
              <li>So sánh với giới hạn của plan hiện tại</li>
              <li>Nếu đạt giới hạn → hiển thị PremiumFeatureModal</li>
              <li>Nếu chưa đạt → cho phép tạo workspace mới</li>
            </ul>
          </div>
        </Card>
      </Card>

      {/* Premium Feature Modal */}
      <PremiumFeatureModal
        visible={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        title="Multiple workspaces is a Premium Feature"
        description="Create unlimited workspaces for each of your projects to keep everything organized and collaborate with different teams."
        onLearnMore={handleLearnMore}
        onTryPremium={handleTryPremium}
      />
    </div>
  );
};

export default WorkspaceWithPremiumLimit;
